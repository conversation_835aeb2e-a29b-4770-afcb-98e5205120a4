<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * 商品エンティティ
 * 
 * @property int $id
 * @property int $maker_id
 * @property int $brand_id
 * @property bool $is_display
 * @property int $year
 * @property string $display_name
 * @property string|null $description_html
 * @property string|null $note_html
 * @property string|null $mask_image_description
 * @property string|null $image_url
 * @property string|null $pdf_url
 * @property string|null $price_range
 * @property string|null $weight_range
 * @property int|null $material
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\Maker $maker
 * @property \App\Model\Entity\Brand $brand
 * @property \App\Model\Entity\Budget[] $budgets
 * @property \App\Model\Entity\RandselOrder[] $randsel_orders
 * @property \App\Model\Entity\RandselInvoice[] $randsel_invoices
 * @property \App\Model\Entity\RandselInvoiceAdjustment[] $randsel_invoice_adjustments
 */
class Product extends Entity
{
    protected $_accessible = [
        'maker_id' => true,
        'brand_id' => true,
        'is_display' => true,
        'year' => true,
        'display_name' => true,
        'description_html' => true,
        'note_html' => true,
        'mask_image_description' => true,
        'image_url' => true,
        'pdf_url' => true,
        'price_range' => true,
        'weight_range' => true,
        'material' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'maker' => true,
        'brand' => true,
        'budgets' => true,
        'randsel_orders' => true,
        'randsel_invoices' => true,
        'randsel_invoice_adjustments' => true,
    ];

    protected $_hidden = [];

    /**
     * 素材名を取得
     */
    public function getMaterialName(): string
    {
        switch ($this->material) {
            case 1:
                return '人工皮革';
            case 2:
                return '天然皮革';
            case 3:
                return '合成皮革';
            case 4:
                return 'その他';
            default:
                return '不明';
        }
    }

    /**
     * 表示可能かどうかを判定
     */
    public function isDisplayable(): bool
    {
        return $this->is_display && !$this->isDeleted();
    }

    /**
     * 論理削除されているかどうかを判定
     */
    public function isDeleted(): bool
    {
        return $this->deleted !== null;
    }

    /**
     * アクティブな商品かどうかを判定
     */
    public function isActive(): bool
    {
        return !$this->isDeleted();
    }

    /**
     * 画像URLが設定されているかどうかを判定
     */
    public function hasImage(): bool
    {
        return !empty($this->image_url);
    }

    /**
     * PDFカタログが設定されているかどうかを判定
     */
    public function hasPdf(): bool
    {
        return !empty($this->pdf_url);
    }

    /**
     * 商品の完全な名前を取得（年度付き）
     */
    public function getFullName(): string
    {
        return $this->year . '年 ' . $this->display_name;
    }
}
