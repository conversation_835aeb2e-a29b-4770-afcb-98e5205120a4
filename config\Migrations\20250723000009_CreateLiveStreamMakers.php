<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class CreateLiveStreamMakers extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('live_stream_makers')
            ->addColumn('live_stream_id', 'integer', [
                'comment' => 'ライブ配信ID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('maker_id', 'integer', [
                'comment' => 'メーカーID',
                'default' => null,
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('created', 'datetime', [
                'comment' => '作成日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
            ])
            ->addColumn('modified', 'datetime', [
                'comment' => '更新日時',
                'default' => 'CURRENT_TIMESTAMP',
                'limit' => null,
                'null' => false,
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addIndex(
                [
                    'live_stream_id',
                ],
                [
                    'name' => 'idx_live_stream_makers_live_stream_id',
                ]
            )
            ->addIndex(
                [
                    'maker_id',
                ],
                [
                    'name' => 'idx_live_stream_makers_maker_id',
                ]
            )
            ->addIndex(
                [
                    'live_stream_id',
                    'maker_id',
                ],
                [
                    'name' => 'uk_live_stream_makers_stream_maker',
                    'unique' => true,
                ]
            )
            ->addIndex(
                [
                    'created',
                ],
                [
                    'name' => 'idx_live_stream_makers_created',
                ]
            )
            ->create();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('live_stream_makers')->drop()->save();
    }
}
